{"name": "rotary-club-mobile", "version": "1.0.0", "private": true, "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"expo": "~49.0.0", "expo-status-bar": "~1.6.0", "react": "18.2.0", "react-native": "0.72.6", "react-native-reanimated": "~3.3.0", "@expo/vector-icons": "^13.0.0", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "react-native-screens": "~3.22.0", "react-native-safe-area-context": "4.6.3", "react-native-gesture-handler": "~2.12.0", "expo-linear-gradient": "~12.3.0", "expo-font": "~11.4.0", "expo-asset": "~8.10.1"}, "devDependencies": {"typescript": "^5.1.6", "@types/react": "^18.2.15", "@types/react-native": "^0.72.2"}}