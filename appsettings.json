{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=rotaryclub;Username=postgres;Password=your_password"}, "Email": {"SmtpServer": "smtp.gmail.com", "SmtpPort": 587, "Username": "<EMAIL>", "Password": "your-app-password", "FromEmail": "<EMAIL>", "FromName": "Rotary Club Mobile"}, "Jwt": {"Key": "your-super-secret-key-here-minimum-16-characters", "Issuer": "RotaryClubMobile", "Audience": "RotaryClubMobile", "ExpiryInMinutes": 60}}