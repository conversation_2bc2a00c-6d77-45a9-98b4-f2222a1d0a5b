{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": false, "jsx": "react-jsx", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "target": "ES2020", "lib": ["ES2020", "DOM"], "moduleResolution": "node"}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts", "types/global.d.ts"], "exclude": ["node_modules"]}